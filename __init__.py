import sys
import os
import bpy
import bpy.props
import re

# Add the 'libs' folder to the Python path FIRST
libs_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "lib")
if libs_path not in sys.path:
    sys.path.append(libs_path)

# Now import dotenv after adding lib to path
from dotenv import load_dotenv

# Load environment variables from .env file if present
load_dotenv()

import openai

from .utilities import *
bl_info = {
    "name": "GPT-4 Blender Assistant",
    "blender": (4, 4, 0),
    "category": "Object",
    "author": "inkbytefo",
    "version": (3, 1, 0),
    "location": "3D View > UI > GPT-4 Blender Assistant",
    "description": "Generate Blender Python code using OpenAI's latest models to perform various tasks.",
    "warning": "",
    "wiki_url": "",
    "tracker_url": "",
}


system_prompt = """You are an assistant made for the purposes of helping the user with Blender 4.4.x and later, the 3D software. 
- Respond with your answers in markdown code blocks (```). 
- Preferably import entire modules instead of bits.
- Use modern Python 3.11+ syntax and features when appropriate.
- Use Blender 4.4+ API features and best practices.
- Do not perform destructive operations on the meshes. 
- Do not use cap_ends. Do not do more than what is asked (setting up render settings, adding cameras, etc)
- Do not respond with anything that is not Python code.
- Use type hints where appropriate for better code readability.
- Use context managers (with statements) when appropriate.
- Prefer Blender's Python API over operators when possible for better performance.

Example:

user: create 10 cubes in random locations from -10 to 10
assistant:
```
import bpy
import random
from typing import List, Tuple

def create_random_cubes(count: int = 10, range_min: int = -10, range_max: int = 10) -> List[bpy.types.Object]:
    '''Create cubes at random locations within the specified range.'''
    created_objects = []
    
    for _ in range(count):
        x = random.randint(range_min, range_max)
        y = random.randint(range_min, range_max)
        z = random.randint(range_min, range_max)
        
        bpy.ops.mesh.primitive_cube_add(location=(x, y, z))
        created_objects.append(bpy.context.active_object)
    
    return created_objects

# Create 10 random cubes
cubes = create_random_cubes(10)
"""





class GPT4_OT_DeleteMessage(bpy.types.Operator):
    bl_idname = "gpt4.delete_message"
    bl_label = "Delete Message"
    bl_options = {'REGISTER', 'UNDO'}

    message_index: bpy.props.IntProperty()

    def execute(self, context):
        context.scene.gpt4_chat_history.remove(self.message_index)
        return {'FINISHED'}

class GPT4_OT_ShowCode(bpy.types.Operator):
    bl_idname = "gpt4.show_code"
    bl_label = "Show Code"
    bl_options = {'REGISTER', 'UNDO'}

    code: bpy.props.StringProperty(
        name="Code",
        description="The generated code",
        default="",
    )

    def execute(self, context):
        text_name = "GPT4_Generated_Code.py"
        text = bpy.data.texts.get(text_name)
        if text is None:
            text = bpy.data.texts.new(text_name)

        text.clear()
        text.write(self.code)

        text_editor_area = None
        for area in context.screen.areas:
            if area.type == 'TEXT_EDITOR':
                text_editor_area = area
                break

        if text_editor_area is None:
            text_editor_area = split_area_to_text_editor(context)
        
        text_editor_area.spaces.active.text = text

        return {'FINISHED'}

class GPT4_PT_Panel(bpy.types.Panel):
    bl_label = "GPT-4 Blender Assistant"
    bl_idname = "GPT4_PT_Panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'GPT-4 Assistant'

    def draw(self, context):
        layout = self.layout
        column = layout.column(align=True)
        
        # Get addon preferences
        preferences = context.preferences.addons[__name__].preferences

        # Model selection section
        box = column.box()
        box.label(text="Model Settings:", icon="OUTLINER_OB_LIGHT")
        box.prop(context.scene, "gpt4_model", text="")
        
        # API type indicator
        api_box = box.box()
        api_box.label(text="API Configuration:")
        api_row = api_box.row()
        api_row.label(text=f"API Type: {preferences.api_type.capitalize()}")
        api_row.operator("wm.addon_userpref_show", text="", icon="PREFERENCES").module = __name__
        
        if preferences.api_type == "compatible":
            api_box.label(text=f"Base URL: {preferences.base_url}")
        
        # Advanced options toggle
        box.prop(context.scene, "gpt4_show_advanced", text="Advanced Options")
        
        # Show advanced options if enabled
        if context.scene.gpt4_show_advanced:
            adv_box = box.box()
            adv_box.label(text="Advanced Settings:")
            adv_box.prop(context.scene, "gpt4_temperature", text="Temperature")
            adv_box.label(text="Lower: More precise, Higher: More creative")

        column.separator()

        # Chat history section
        column.label(text="Chat History:", icon="SPEECH_BUBBLE")
        history_box = column.box()
        
        if len(context.scene.gpt4_chat_history) == 0:
            history_box.label(text="No messages yet. Start by entering a prompt below.")
        
        for index, message in enumerate(context.scene.gpt4_chat_history):
            if message.type == 'assistant':
                row = history_box.row()
                row.label(text="Assistant: ", icon="BOT")
                show_code_op = row.operator("gpt4.show_code", text="Show Code", icon="TEXT")
                show_code_op.code = message.content
                delete_message_op = row.operator("gpt4.delete_message", text="", icon="TRASH", emboss=False)
                delete_message_op.message_index = index
            else:
                row = history_box.row()
                row.label(text=f"User: {message.content}", icon="USER")
                delete_message_op = row.operator("gpt4.delete_message", text="", icon="TRASH", emboss=False)
                delete_message_op.message_index = index

        column.separator()
        
        # Input section
        input_box = column.box()
        input_box.label(text="Enter your prompt:", icon="CONSOLE")
        input_box.prop(context.scene, "gpt4_chat_input", text="")
        
        # Button row
        button_label = "Please wait..." if context.scene.gpt4_button_pressed else "Execute"
        button_icon = "TEMP" if context.scene.gpt4_button_pressed else "PLAY"
        
        row = input_box.row(align=True)
        row.scale_y = 1.5
        row.operator("gpt4.send_message", text=button_label, icon=button_icon)
        row.operator("gpt4.clear_chat", text="Clear Chat", icon="X")

        column.separator()

class GPT4_OT_ClearChat(bpy.types.Operator):
    bl_idname = "gpt4.clear_chat"
    bl_label = "Clear Chat"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        context.scene.gpt4_chat_history.clear()
        return {'FINISHED'}

class GPT4_OT_Execute(bpy.types.Operator):
    bl_idname = "gpt4.send_message"
    bl_label = "Send Message"
    bl_options = {'REGISTER', 'UNDO'}

    natural_language_input: bpy.props.StringProperty(
        name="Command",
        description="Enter the natural language command",
        default="",
    )

    def execute(self, context):
        # Get addon preferences
        preferences = context.preferences.addons[__name__].preferences
        api_key = preferences.api_key
        api_type = preferences.api_type
        base_url = preferences.base_url if api_type == "compatible" else None
        
        # if null then set to env key
        if not api_key:
            api_key = os.getenv("OPENAI_API_KEY")

        if not api_key:
            self.report({'ERROR'}, "No API key detected. Please set the API key in the addon preferences.")
            return {'CANCELLED'}
            
        # Set the API key for the OpenAI client
        os.environ["OPENAI_API_KEY"] = api_key

        context.scene.gpt4_button_pressed = True
        bpy.ops.wm.redraw_timer(type='DRAW_WIN_SWAP', iterations=1)
        
        try:
            blender_code = generate_blender_code(
                prompt=context.scene.gpt4_chat_input, 
                chat_history=context.scene.gpt4_chat_history, 
                context=context, 
                system_prompt=system_prompt,
                api_type=api_type,
                base_url=base_url
            )

            message = context.scene.gpt4_chat_history.add()
            message.type = 'user'
            message.content = context.scene.gpt4_chat_input

            # Clear the chat input field
            context.scene.gpt4_chat_input = ""

            if blender_code:
                message = context.scene.gpt4_chat_history.add()
                message.type = 'assistant'
                message.content = blender_code

                global_namespace = globals().copy()
        
                try:
                    exec(blender_code, global_namespace)
                except Exception as e:
                    self.report({'ERROR'}, f"Error executing generated code: {e}")
                    context.scene.gpt4_button_pressed = False
                    return {'CANCELLED'}
            else:
                self.report({'WARNING'}, "Failed to generate code. Please try again with a different prompt.")
                context.scene.gpt4_button_pressed = False
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error communicating with API: {e}")
            context.scene.gpt4_button_pressed = False
            return {'CANCELLED'}

        context.scene.gpt4_button_pressed = False
        return {'FINISHED'}


def menu_func(self, context):
    self.layout.operator(GPT4_OT_Execute.bl_idname)

class GPT4AddonPreferences(bpy.types.AddonPreferences):
    bl_idname = __name__

    api_key: bpy.props.StringProperty(
        name="API Key",
        description="Enter your OpenAI API Key",
        default="",
        subtype="PASSWORD",
    )
    
    api_type: bpy.props.EnumProperty(
        name="API Type",
        description="Select the API type to use",
        items=[
            ("openai", "OpenAI API", "Use the official OpenAI API"),
            ("compatible", "OpenAI Compatible API", "Use an OpenAI-compatible API (e.g., LM Studio, Ollama)"),
        ],
        default="openai",
    )
    
    base_url: bpy.props.StringProperty(
        name="Base URL",
        description="Base URL for OpenAI-compatible API (e.g., http://localhost:1234/v1)",
        default="http://localhost:1234/v1",
    )

    def draw(self, context):
        layout = self.layout
        layout.prop(self, "api_type")
        layout.prop(self, "api_key")
        
        # Only show base URL field if using compatible API
        if self.api_type == "compatible":
            layout.prop(self, "base_url")

def register():
    bpy.utils.register_class(GPT4AddonPreferences)
    bpy.utils.register_class(GPT4_OT_Execute)
    bpy.utils.register_class(GPT4_PT_Panel)
    bpy.utils.register_class(GPT4_OT_ClearChat)
    bpy.utils.register_class(GPT4_OT_ShowCode)
    bpy.utils.register_class(GPT4_OT_DeleteMessage)


    bpy.types.VIEW3D_MT_mesh_add.append(menu_func)
    init_props()


def unregister():
    bpy.utils.unregister_class(GPT4AddonPreferences)
    bpy.utils.unregister_class(GPT4_OT_Execute)
    bpy.utils.unregister_class(GPT4_PT_Panel)
    bpy.utils.unregister_class(GPT4_OT_ClearChat)
    bpy.utils.unregister_class(GPT4_OT_ShowCode)
    bpy.utils.unregister_class(GPT4_OT_DeleteMessage)

    bpy.types.VIEW3D_MT_mesh_add.remove(menu_func)
    clear_props()


if __name__ == "__main__":
    register()
